using FluentValidation;
using Shared.Domain.Constants;

namespace Reports.Application.Features.Web.Queries.Reports.ExportMassBalance;

/// <summary>
/// Validator for Mass Balance export requests
/// </summary>
public class ExportMassBalanceValidator : AbstractValidator<ExportMassBalanceRequest>
{
    public ExportMassBalanceValidator()
    {
        RuleFor(x => x.FromDate)
            .NotEmpty()
            .WithMessage("FromDate is required");

        RuleFor(x => x.ToDate)
            .NotEmpty()
            .WithMessage("ToDate is required")
            .GreaterThan(x => x.FromDate)
            .WithMessage("ToDate must be greater than FromDate");

        RuleFor(x => x.ExcelFormat)
            .Must(BeValidExcelFormat)
            .When(x => !string.IsNullOrEmpty(x.ExcelFormat))
            .WithMessage("ExcelFormat must be one of: xlsx, xls, csv");
    }

    private static bool BeValidExcelFormat(string? format)
    {
        if (string.IsNullOrEmpty(format))
            return true;

        return Enum.TryParse<ExcelFormat>(format, ignoreCase: true, out _);
    }
}
