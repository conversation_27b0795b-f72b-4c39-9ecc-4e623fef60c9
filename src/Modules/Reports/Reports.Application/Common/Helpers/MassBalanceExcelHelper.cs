using NPOI.SS.UserModel;
using NPOI.SS.Util;
using NPOI.XSSF.UserModel;
using Reports.Application.Common.Constants;
using Reports.Application.DTOs.Reports.MassBalance;

namespace Reports.Application.Common.Helpers;

/// <summary>
/// Helper class for Mass Balance Excel generation and styling
/// </summary>
public static class MassBalanceExcelHelper
{
    #region Public Methods

    /// <summary>
    /// Generates a complete Mass Balance Excel workbook
    /// </summary>
    /// <param name="exportData">The data to export</param>
    /// <returns>Excel file as byte array</returns>
    public static byte[] GenerateExcel(MassBalanceExportDto exportData)
    {
        var workbook = new XSSFWorkbook();
        var sheet = workbook.CreateSheet(MassBalanceExcelConstants.MainTitle);

        var currentRow = 0;
        
        // Create header section
        currentRow = CreateHeader(sheet, currentRow, exportData.Title, exportData.GenerationDate, exportData.Period);
        
        // Create main balance sheet table
        currentRow = CreateBalanceSheetTable(sheet, currentRow, exportData.BalanceSheetRows);
        
        // Create final disposition summary table
        currentRow = CreateSummaryTable(sheet, currentRow, exportData.FinalDispositionSummary);
        
        // Create distribution table
        currentRow = CreateDistributionTable(sheet, currentRow, exportData.DistributionRows);
        
        // Apply formatting
        ApplyWorkbookFormatting(sheet);

        // Auto-size and set specific column widths
        SetColumnWidths(sheet);

        using var stream = new MemoryStream();
        workbook.Write(stream);
        return stream.ToArray();
    }

    #endregion

    #region Table Creation Methods

    /// <summary>
    /// Creates the header section with title, generation date, and period
    /// </summary>
    private static int CreateHeader(ISheet sheet, int startRow, string title, DateTime generationDate, string period)
    {
        var currentRow = startRow;

        // Title row
        var titleRow = sheet.CreateRow(currentRow++);
        var titleCell = titleRow.CreateCell(0);
        titleCell.SetCellValue(title);

        // Merge title across columns and create all cells in the merged range
        var titleMergeRegion = new CellRangeAddress(startRow, startRow, 0, MassBalanceExcelConstants.BalanceSheetColumnCount - 1);
        sheet.AddMergedRegion(titleMergeRegion);
        CreateCellsInRange(titleRow, 1, MassBalanceExcelConstants.BalanceSheetColumnCount - 1);

        // Generation date row
        var dateRow = sheet.CreateRow(currentRow++);
        var dateCell = dateRow.CreateCell(0);
        dateCell.SetCellValue($"Generado: {generationDate:dd/MM/yyyy HH:mm}");

        // Period row
        var periodRow = sheet.CreateRow(currentRow++);
        var periodCell = periodRow.CreateCell(0);
        periodCell.SetCellValue($"Período: {period}");

        // Empty row for spacing
        sheet.CreateRow(currentRow++);

        return currentRow;
    }

    /// <summary>
    /// Creates the main balance sheet table
    /// </summary>
    private static int CreateBalanceSheetTable(ISheet sheet, int startRow, List<BalanceSheetRowDto> data)
    {
        var currentRow = startRow;

        // Create main header
        currentRow = CreateTableHeader(sheet, currentRow, MassBalanceExcelConstants.BalanceSheetTitle, MassBalanceExcelConstants.BalanceSheetColumnCount);
        
        // Create column headers
        currentRow = CreateColumnHeaders(sheet, currentRow, MassBalanceExcelConstants.BalanceSheetHeaders);

        // Create data rows
        var dataStartRow = currentRow + 1;
        foreach (var row in data)
        {
            var dataRow = sheet.CreateRow(currentRow++);
            PopulateBalanceSheetDataRow(dataRow, row, currentRow);
        }

        // Create totals row
        currentRow = CreateBalanceSheetTotalsRow(sheet, currentRow, dataStartRow, currentRow - 1);

        // Add spacing
        AddSectionSpacing(sheet, ref currentRow);

        return currentRow;
    }

    /// <summary>
    /// Creates the final disposition summary table
    /// </summary>
    private static int CreateSummaryTable(ISheet sheet, int startRow, FinalDispositionSummaryDto summary)
    {
        var currentRow = startRow;

        // Create table header
        currentRow = CreateTableHeader(sheet, currentRow, MassBalanceExcelConstants.SummaryTitle, MassBalanceExcelConstants.SummaryTableColumnCount);
        
        // Create column headers
        currentRow = CreateColumnHeaders(sheet, currentRow, MassBalanceExcelConstants.SummaryHeaders);

        // Create data rows
        currentRow = CreateSummaryDataRows(sheet, currentRow, summary);

        // Add spacing
        AddSectionSpacing(sheet, ref currentRow);

        return currentRow;
    }

    /// <summary>
    /// Creates the distribution table
    /// </summary>
    private static int CreateDistributionTable(ISheet sheet, int startRow, List<DistributionRowDto> data)
    {
        var currentRow = startRow;

        // Create table header
        currentRow = CreateTableHeader(sheet, currentRow, MassBalanceExcelConstants.DistributionTitle, MassBalanceExcelConstants.DistributionTableColumnCount);
        
        // Create column headers
        currentRow = CreateColumnHeaders(sheet, currentRow, MassBalanceExcelConstants.DistributionHeaders);

        // Create data rows
        var dataStartRow = currentRow + 1;
        foreach (var row in data)
        {
            var dataRow = sheet.CreateRow(currentRow++);
            PopulateDistributionDataRow(dataRow, row);
        }

        // Create totals row
        currentRow = CreateDistributionTotalsRow(sheet, currentRow, dataStartRow, currentRow - 1);

        return currentRow;
    }

    #endregion

    #region Helper Methods

    /// <summary>
    /// Creates a table header with proper merging
    /// </summary>
    private static int CreateTableHeader(ISheet sheet, int startRow, string title, int columnCount)
    {
        var headerRow = sheet.CreateRow(startRow);
        var headerCell = headerRow.CreateCell(0);
        headerCell.SetCellValue(title);

        // Merge header across columns
        var mergeRegion = new CellRangeAddress(startRow, startRow, 0, columnCount - 1);
        sheet.AddMergedRegion(mergeRegion);
        CreateCellsInRange(headerRow, 1, columnCount - 1);

        return startRow + 1;
    }

    /// <summary>
    /// Creates column headers for a table
    /// </summary>
    private static int CreateColumnHeaders(ISheet sheet, int startRow, string[] headers)
    {
        var headerRow = sheet.CreateRow(startRow);
        for (int i = 0; i < headers.Length; i++)
        {
            var cell = headerRow.CreateCell(i);
            cell.SetCellValue(headers[i]);
        }
        return startRow + 1;
    }

    /// <summary>
    /// Populates a balance sheet data row
    /// </summary>
    private static void PopulateBalanceSheetDataRow(IRow dataRow, BalanceSheetRowDto row, int rowNumber)
    {
        dataRow.CreateCell(0).SetCellValue(row.AreaCode);
        dataRow.CreateCell(1).SetCellValue(row.AreaName);
        dataRow.CreateCell(2).SetCellValue((double)row.UrbanCleaningTons);
        dataRow.CreateCell(3).SetCellValue((double)row.SweepingTons);
        dataRow.CreateCell(4).SetCellValue((double)row.NonRecyclableTons);
        dataRow.CreateCell(5).SetCellValue((double)row.RejectionTons);
        dataRow.CreateCell(6).SetCellValue((double)row.RecyclableTons);

        // Formula: Barrido + No Aprovechables
        var totalF14Cell = dataRow.CreateCell(7);
        totalF14Cell.SetCellFormula($"D{rowNumber}+E{rowNumber}");

        dataRow.CreateCell(8).SetCellValue((double)row.TotalByNUAP);
        dataRow.CreateCell(9).SetCellValue((double)row.Discounts);

        // Formula: Total NUAP - Descuentos
        var totalF34Cell = dataRow.CreateCell(10);
        totalF34Cell.SetCellFormula($"I{rowNumber}-J{rowNumber}");

        // Formula: Diferencia F34-F14
        var differenceCell = dataRow.CreateCell(11);
        differenceCell.SetCellFormula($"K{rowNumber}-H{rowNumber}");
    }

    /// <summary>
    /// Populates a distribution data row
    /// </summary>
    private static void PopulateDistributionDataRow(IRow dataRow, DistributionRowDto row)
    {
        dataRow.CreateCell(0).SetCellValue(row.RecyclingArea);
        dataRow.CreateCell(1).SetCellValue((double)row.ReportedTons);
        dataRow.CreateCell(2).SetCellValue(row.Trips);
        dataRow.CreateCell(3).SetCellValue((double)row.CalculatedDistributedTons);
        dataRow.CreateCell(4).SetCellValue((double)row.TollSharedRouteTons);
        dataRow.CreateCell(5).SetCellValue((double)row.CalculatedDistributionTollPercentage);
        dataRow.CreateCell(6).SetCellValue((double)row.CalculatedDeviationTons);
    }

    /// <summary>
    /// Creates summary table data rows
    /// </summary>
    private static int CreateSummaryDataRows(ISheet sheet, int startRow, FinalDispositionSummaryDto summary)
    {
        var currentRow = startRow;

        // Weighing data row
        var weighingRow = sheet.CreateRow(currentRow++);
        weighingRow.CreateCell(0).SetCellValue(MassBalanceExcelConstants.SummaryRowLabels[0]);
        weighingRow.CreateCell(1).SetCellValue((double)summary.WeighingEmvariasTons);
        weighingRow.CreateCell(2).SetCellValue((double)summary.WeighingTotalTons);
        weighingRow.CreateCell(3).SetCellValue(MassBalanceExcelConstants.ZeroValue);

        // Final disposition data row
        var finalDispRow = sheet.CreateRow(currentRow++);
        finalDispRow.CreateCell(0).SetCellValue(MassBalanceExcelConstants.SummaryRowLabels[1]);
        finalDispRow.CreateCell(1).SetCellValue((double)summary.FinalDispositionEmvariasTons);
        finalDispRow.CreateCell(2).SetCellValue((double)summary.FinalDispositionTotalTons);
        finalDispRow.CreateCell(3).SetCellValue((double)summary.FinalDispositionDiscountTons);

        return currentRow;
    }

    /// <summary>
    /// Creates cells in a range for merged regions
    /// </summary>
    private static void CreateCellsInRange(IRow row, int startCol, int endCol)
    {
        for (int col = startCol; col <= endCol; col++)
        {
            row.CreateCell(col);
        }
    }

    /// <summary>
    /// Adds section spacing
    /// </summary>
    private static void AddSectionSpacing(ISheet sheet, ref int currentRow)
    {
        for (int i = 0; i < MassBalanceExcelConstants.SectionSpacingRows; i++)
        {
            sheet.CreateRow(currentRow++);
        }
    }

    /// <summary>
    /// Gets column letter for Excel formulas
    /// </summary>
    private static string GetColumnLetter(int columnIndex)
    {
        if (columnIndex < 26)
            return MassBalanceExcelConstants.ColumnLetters[columnIndex].ToString();

        return MassBalanceExcelConstants.ColumnLetters[columnIndex / 26 - 1].ToString() + 
               MassBalanceExcelConstants.ColumnLetters[columnIndex % 26].ToString();
    }

    /// <summary>
    /// Creates balance sheet totals row
    /// </summary>
    private static int CreateBalanceSheetTotalsRow(ISheet sheet, int currentRow, int dataStartRow, int dataEndRow)
    {
        var totalsRow = sheet.CreateRow(currentRow++);
        totalsRow.CreateCell(1).SetCellValue(MassBalanceExcelConstants.BalanceSheetTotalLabel);

        for (int col = 2; col <= MassBalanceExcelConstants.BalanceSheetColumnCount - 1; col++)
        {
            var totalCell = totalsRow.CreateCell(col);
            totalCell.SetCellFormula($"SUM({GetColumnLetter(col)}{dataStartRow}:{GetColumnLetter(col)}{dataEndRow})");
        }

        return currentRow;
    }

    /// <summary>
    /// Creates distribution totals row
    /// </summary>
    private static int CreateDistributionTotalsRow(ISheet sheet, int currentRow, int dataStartRow, int dataEndRow)
    {
        var totalsRow = sheet.CreateRow(currentRow++);
        totalsRow.CreateCell(0).SetCellValue(MassBalanceExcelConstants.DistributionTotalLabel);

        for (int col = 1; col <= MassBalanceExcelConstants.DistributionTableColumnCount - 1; col++)
        {
            var totalCell = totalsRow.CreateCell(col);

            if (col == 3) // Tonxviaje column - use SUMPRODUCT
            {
                totalCell.SetCellFormula($"SUMPRODUCT({GetColumnLetter(2)}{dataStartRow}:{GetColumnLetter(2)}{dataEndRow},{GetColumnLetter(3)}{dataStartRow}:{GetColumnLetter(3)}{dataEndRow})");
            }
            else if (col == 4) // Toneladas Totales Rutas Compartidas - mark as "-"
            {
                totalCell.SetCellValue(MassBalanceExcelConstants.DashValue);
            }
            else
            {
                totalCell.SetCellFormula($"SUM({GetColumnLetter(col)}{dataStartRow}:{GetColumnLetter(col)}{dataEndRow})");
            }
        }

        return currentRow;
    }

    /// <summary>
    /// Sets column widths for the worksheet
    /// </summary>
    private static void SetColumnWidths(ISheet sheet)
    {
        // Auto-size most columns
        for (int i = 0; i < 15; i++)
        {
            sheet.AutoSizeColumn(i);
        }

        // Set specific widths for certain columns
        sheet.SetColumnWidth(0, MassBalanceExcelConstants.TimestampColumnWidth);
        sheet.SetColumnWidth(8, MassBalanceExcelConstants.TotalByNuapColumnWidth);
    }

    #endregion

    #region Formatting Methods

    /// <summary>
    /// Applies all formatting to the workbook
    /// </summary>
    private static void ApplyWorkbookFormatting(ISheet sheet)
    {
        var workbook = sheet.Workbook;
        var styles = CreateAllStyles(workbook);

        ApplyTitleFormatting(sheet, styles.TitleStyle);
        ApplyHeaderFormatting(sheet, styles.HeaderStyle);
        ApplyDataFormatting(sheet, styles);
        ApplyTotalFormatting(sheet, styles.TotalStyle);
        ApplyDistributionTableBorders(sheet, workbook);
        ApplyBordersToMergedRegions(sheet);
    }

    /// <summary>
    /// Creates all required cell styles
    /// </summary>
    private static ExcelStyles CreateAllStyles(IWorkbook workbook)
    {
        return new ExcelStyles
        {
            TitleStyle = CreateTitleStyle(workbook),
            HeaderStyle = CreateHeaderStyle(workbook),
            DataStyle = CreateDataStyle(workbook),
            IntegerStyle = CreateIntegerStyle(workbook),
            CalculatedStyle = CreateCalculatedStyle(workbook),
            CompensationStyle = CreateCompensationStyle(workbook),
            TotalStyle = CreateTotalStyle(workbook)
        };
    }

    /// <summary>
    /// Creates title style
    /// </summary>
    private static ICellStyle CreateTitleStyle(IWorkbook workbook)
    {
        var style = workbook.CreateCellStyle();
        var font = workbook.CreateFont();
        font.IsBold = true;
        font.FontHeightInPoints = MassBalanceExcelConstants.TitleFontSize;
        font.Color = IndexedColors.White.Index;
        style.SetFont(font);
        style.Alignment = HorizontalAlignment.Center;
        style.FillForegroundColor = IndexedColors.Violet.Index;
        style.FillPattern = FillPattern.SolidForeground;
        return style;
    }

    /// <summary>
    /// Creates header style
    /// </summary>
    private static ICellStyle CreateHeaderStyle(IWorkbook workbook)
    {
        var style = workbook.CreateCellStyle();
        var font = workbook.CreateFont();
        font.IsBold = true;
        font.Color = IndexedColors.White.Index;
        style.SetFont(font);
        style.Alignment = HorizontalAlignment.Center;
        style.FillForegroundColor = IndexedColors.Violet.Index;
        style.FillPattern = FillPattern.SolidForeground;
        style.BorderTop = BorderStyle.Thick;
        style.BorderBottom = BorderStyle.Thick;
        style.BorderLeft = BorderStyle.Thick;
        style.BorderRight = BorderStyle.Thick;
        return style;
    }

    /// <summary>
    /// Creates data style
    /// </summary>
    private static ICellStyle CreateDataStyle(IWorkbook workbook)
    {
        var style = workbook.CreateCellStyle();
        style.DataFormat = workbook.CreateDataFormat().GetFormat(MassBalanceExcelConstants.DecimalFormat);
        style.BorderTop = BorderStyle.Thin;
        style.BorderBottom = BorderStyle.Thin;
        style.BorderLeft = BorderStyle.Thin;
        style.BorderRight = BorderStyle.Thin;
        return style;
    }

    /// <summary>
    /// Creates integer style
    /// </summary>
    private static ICellStyle CreateIntegerStyle(IWorkbook workbook)
    {
        var style = workbook.CreateCellStyle();
        style.DataFormat = workbook.CreateDataFormat().GetFormat(MassBalanceExcelConstants.IntegerFormat);
        style.BorderTop = BorderStyle.Thin;
        style.BorderBottom = BorderStyle.Thin;
        style.BorderLeft = BorderStyle.Thin;
        style.BorderRight = BorderStyle.Thin;
        return style;
    }

    /// <summary>
    /// Creates calculated style
    /// </summary>
    private static ICellStyle CreateCalculatedStyle(IWorkbook workbook)
    {
        var style = workbook.CreateCellStyle();
        var font = workbook.CreateFont();
        font.IsBold = true;
        style.SetFont(font);
        style.DataFormat = workbook.CreateDataFormat().GetFormat(MassBalanceExcelConstants.DecimalFormat);
        style.FillForegroundColor = IndexedColors.LightYellow.Index;
        style.FillPattern = FillPattern.SolidForeground;
        style.BorderTop = BorderStyle.Thick;
        style.BorderBottom = BorderStyle.Thick;
        style.BorderLeft = BorderStyle.Thick;
        style.BorderRight = BorderStyle.Thick;
        return style;
    }

    /// <summary>
    /// Creates compensation style
    /// </summary>
    private static ICellStyle CreateCompensationStyle(IWorkbook workbook)
    {
        var style = workbook.CreateCellStyle();
        var font = workbook.CreateFont();
        font.IsBold = true;
        style.SetFont(font);
        style.DataFormat = workbook.CreateDataFormat().GetFormat(MassBalanceExcelConstants.CompensationFormat);
        style.FillForegroundColor = IndexedColors.LightYellow.Index;
        style.FillPattern = FillPattern.SolidForeground;
        style.BorderTop = BorderStyle.Thick;
        style.BorderBottom = BorderStyle.Thick;
        style.BorderLeft = BorderStyle.Thick;
        style.BorderRight = BorderStyle.Thick;
        return style;
    }

    /// <summary>
    /// Creates total style
    /// </summary>
    private static ICellStyle CreateTotalStyle(IWorkbook workbook)
    {
        var style = workbook.CreateCellStyle();
        var font = workbook.CreateFont();
        font.IsBold = true;
        style.SetFont(font);
        style.DataFormat = workbook.CreateDataFormat().GetFormat(MassBalanceExcelConstants.DecimalFormat);
        style.FillForegroundColor = IndexedColors.Grey25Percent.Index;
        style.FillPattern = FillPattern.SolidForeground;
        style.BorderTop = BorderStyle.Thick;
        style.BorderBottom = BorderStyle.Thick;
        style.BorderLeft = BorderStyle.Thick;
        style.BorderRight = BorderStyle.Thick;
        return style;
    }

    /// <summary>
    /// Applies title formatting to the first 3 rows
    /// </summary>
    private static void ApplyTitleFormatting(ISheet sheet, ICellStyle titleStyle)
    {
        for (int row = 0; row < 3; row++)
        {
            var sheetRow = sheet.GetRow(row);
            if (sheetRow != null)
            {
                for (int col = 0; col < MassBalanceExcelConstants.BalanceSheetColumnCount; col++)
                {
                    var cell = sheetRow.GetCell(col);
                    if (cell != null)
                    {
                        cell.CellStyle = titleStyle;
                    }
                }
            }
        }
    }

    /// <summary>
    /// Applies header formatting to all header rows
    /// </summary>
    private static void ApplyHeaderFormatting(ISheet sheet, ICellStyle headerStyle)
    {
        // Apply to all section headers dynamically based on content
        for (int rowIndex = 0; rowIndex <= sheet.LastRowNum; rowIndex++)
        {
            var row = sheet.GetRow(rowIndex);
            if (row != null)
            {
                var firstCell = row.GetCell(0);
                if (firstCell != null)
                {
                    var cellValue = firstCell.StringCellValue;
                    var columnCount = GetColumnCountForHeader(cellValue);

                    if (columnCount > 0)
                    {
                        ApplyStyleToRowRange(sheet, rowIndex, headerStyle, 0, columnCount - 1);
                    }
                }
            }
        }
    }

    /// <summary>
    /// Applies data formatting to data rows
    /// </summary>
    private static void ApplyDataFormatting(ISheet sheet, ExcelStyles styles)
    {
        for (int rowIndex = 0; rowIndex <= sheet.LastRowNum; rowIndex++)
        {
            var row = sheet.GetRow(rowIndex);
            if (row != null && IsDataRow(row))
            {
                ApplyDataRowFormatting(row, styles);
            }
        }
    }

    /// <summary>
    /// Applies total formatting to total rows
    /// </summary>
    private static void ApplyTotalFormatting(ISheet sheet, ICellStyle totalStyle)
    {
        for (int rowIndex = 0; rowIndex <= sheet.LastRowNum; rowIndex++)
        {
            var row = sheet.GetRow(rowIndex);
            if (row != null && IsTotalRow(row))
            {
                var columnCount = GetColumnCountForTotalRow(sheet, rowIndex);
                ApplyStyleToRowRange(sheet, rowIndex, totalStyle, 0, columnCount - 1);
            }
        }
    }

    /// <summary>
    /// Applies specialized borders to distribution table
    /// </summary>
    private static void ApplyDistributionTableBorders(ISheet sheet, IWorkbook workbook)
    {
        var (startRow, endRow) = FindDistributionTableBounds(sheet);
        if (startRow == -1 || endRow == -1) return;

        for (int rowIndex = startRow; rowIndex <= endRow; rowIndex++)
        {
            var row = sheet.GetRow(rowIndex);
            if (row != null)
            {
                ApplyDistributionRowBorders(row, workbook, rowIndex, startRow, endRow);
            }
        }
    }

    /// <summary>
    /// Applies borders to merged regions
    /// </summary>
    private static void ApplyBordersToMergedRegions(ISheet sheet)
    {
        var workbook = sheet.Workbook;

        for (int i = 0; i < sheet.NumMergedRegions; i++)
        {
            var mergedRegion = sheet.GetMergedRegion(i);
            ApplyBordersToMergedRegion(sheet, workbook, mergedRegion);
        }
    }

    /// <summary>
    /// Applies style to a range of cells in a row
    /// </summary>
    private static void ApplyStyleToRowRange(ISheet sheet, int rowIndex, ICellStyle style, int startCol, int endCol)
    {
        var row = sheet.GetRow(rowIndex);
        if (row != null)
        {
            for (int col = startCol; col <= endCol; col++)
            {
                var cell = row.GetCell(col);
                if (cell != null)
                {
                    cell.CellStyle = style;
                }
            }
        }
    }

    /// <summary>
    /// Gets column count based on header text
    /// </summary>
    private static int GetColumnCountForHeader(string cellValue)
    {
        return cellValue switch
        {
            var value when value.Contains(MassBalanceExcelConstants.BalanceSheetTitle) => MassBalanceExcelConstants.BalanceSheetColumnCount,
            MassBalanceExcelConstants.SummaryTitle => MassBalanceExcelConstants.SummaryTableColumnCount,
            MassBalanceExcelConstants.ConceptKeyword => MassBalanceExcelConstants.SummaryTableColumnCount,
            MassBalanceExcelConstants.DistributionTitle => MassBalanceExcelConstants.DistributionTableColumnCount,
            MassBalanceExcelConstants.RecyclingAreaKeyword => MassBalanceExcelConstants.DistributionTableColumnCount,
            // Check for balance sheet column headers (first column header)
            var value when value == MassBalanceExcelConstants.BalanceSheetHeaders[0] => MassBalanceExcelConstants.BalanceSheetColumnCount,
            // Check for summary table column headers (first column header)
            var value when value == MassBalanceExcelConstants.SummaryHeaders[0] => MassBalanceExcelConstants.SummaryTableColumnCount,
            // Check for distribution table column headers (first column header)
            var value when value == MassBalanceExcelConstants.DistributionHeaders[0] => MassBalanceExcelConstants.DistributionTableColumnCount,
            _ => 0
        };
    }

    /// <summary>
    /// Checks if a row is a data row (not header or total)
    /// </summary>
    private static bool IsDataRow(IRow row)
    {
        var firstCell = row.GetCell(0) ?? row.GetCell(1);
        var cellValue = firstCell?.StringCellValue ?? "";

        // Exclude total rows
        if (cellValue.Contains(MassBalanceExcelConstants.TotalKeyword) || cellValue.Contains(MassBalanceExcelConstants.SumKeyword))
            return false;

        // Exclude table headers and section titles
        if (cellValue.Contains(MassBalanceExcelConstants.SummaryTitle) || cellValue.Contains(MassBalanceExcelConstants.DistributionTitle) ||
            cellValue.Contains(MassBalanceExcelConstants.BalanceSheetTitle))
            return false;

        // Exclude column headers
        if (cellValue == MassBalanceExcelConstants.ConceptKeyword || cellValue == MassBalanceExcelConstants.RecyclingAreaKeyword ||
            cellValue == MassBalanceExcelConstants.BalanceSheetHeaders[0] || cellValue == MassBalanceExcelConstants.SummaryHeaders[0] ||
            cellValue == MassBalanceExcelConstants.DistributionHeaders[0])
            return false;

        // Exclude empty rows or rows with only whitespace
        if (string.IsNullOrWhiteSpace(cellValue))
            return false;

        return true;
    }

    /// <summary>
    /// Checks if a row is a total row
    /// </summary>
    private static bool IsTotalRow(IRow row)
    {
        var firstCell = row.GetCell(0) ?? row.GetCell(1);
        return firstCell != null && (firstCell.StringCellValue.Contains(MassBalanceExcelConstants.TotalKeyword) || firstCell.StringCellValue.Contains(MassBalanceExcelConstants.SumKeyword));
    }

    /// <summary>
    /// Applies formatting to a data row
    /// </summary>
    private static void ApplyDataRowFormatting(IRow row, ExcelStyles styles)
    {
        // Balance sheet formatting
        for (int col = 0; col < MassBalanceExcelConstants.BalanceSheetColumnCount; col++)
        {
            var cell = row.GetCell(col);
            if (cell != null)
            {
                if (MassBalanceExcelConstants.BalanceSheetFormulaColumns.Contains(col))
                {
                    cell.CellStyle = styles.CalculatedStyle;
                }
                else if (col >= 2 || (col <= 1 && cell.CellType != CellType.Blank))
                {
                    cell.CellStyle = styles.DataStyle;
                }
            }
        }

        // Distribution table formatting
        for (int col = 0; col < MassBalanceExcelConstants.DistributionTableColumnCount; col++)
        {
            var cell = row.GetCell(col);
            if (cell != null)
            {
                if (MassBalanceExcelConstants.DistributionCompensationColumns.Contains(col))
                {
                    cell.CellStyle = styles.CompensationStyle;
                }
                else if (MassBalanceExcelConstants.DistributionIntegerColumns.Contains(col))
                {
                    cell.CellStyle = styles.IntegerStyle;
                }
                else
                {
                    cell.CellStyle = styles.DataStyle;
                }
            }
        }
    }

    /// <summary>
    /// Gets column count for total row based on context
    /// </summary>
    private static int GetColumnCountForTotalRow(ISheet sheet, int rowIndex)
    {
        // Look for distribution table context
        for (int checkRow = rowIndex - 1; checkRow >= 0; checkRow--)
        {
            var checkRowObj = sheet.GetRow(checkRow);
            if (checkRowObj != null)
            {
                var checkCell = checkRowObj.GetCell(0);
                if (checkCell != null)
                {
                    var cellValue = checkCell.StringCellValue;
                    if (cellValue == MassBalanceExcelConstants.DistributionTitle)
                    {
                        return MassBalanceExcelConstants.DistributionTableColumnCount;
                    }
                    else if (cellValue.Contains(MassBalanceExcelConstants.BalanceSheetTitle))
                    {
                        return MassBalanceExcelConstants.BalanceSheetColumnCount;
                    }
                }
            }
        }
        return MassBalanceExcelConstants.BalanceSheetColumnCount; // Default
    }

    /// <summary>
    /// Finds distribution table boundaries
    /// </summary>
    private static (int startRow, int endRow) FindDistributionTableBounds(ISheet sheet)
    {
        int startRow = -1, endRow = -1;

        for (int rowIndex = 0; rowIndex <= sheet.LastRowNum; rowIndex++)
        {
            var row = sheet.GetRow(rowIndex);
            if (row != null)
            {
                var firstCell = row.GetCell(0);
                if (firstCell != null)
                {
                    var cellValue = firstCell.StringCellValue;
                    if (cellValue == MassBalanceExcelConstants.DistributionTitle)
                    {
                        startRow = rowIndex;
                    }
                    else if (startRow != -1 && cellValue == MassBalanceExcelConstants.DistributionTotalLabel)
                    {
                        endRow = rowIndex;
                        break;
                    }
                }
            }
        }

        return (startRow, endRow);
    }

    /// <summary>
    /// Applies borders to distribution table row
    /// </summary>
    private static void ApplyDistributionRowBorders(IRow row, IWorkbook workbook, int rowIndex, int startRow, int endRow)
    {
        bool isTopRow = rowIndex == startRow;
        bool isBottomRow = rowIndex == endRow;
        bool isHeaderRow = IsDistributionHeaderRow(row);

        for (int col = 0; col < MassBalanceExcelConstants.DistributionTableColumnCount; col++)
        {
            var cell = row.GetCell(col);
            if (cell != null)
            {
                bool isLeftColumn = col == 0;
                bool isRightColumn = col == MassBalanceExcelConstants.DistributionTableColumnCount - 1;

                var borderStyle = CreateDistributionBorderStyle(workbook, col, isHeaderRow, isBottomRow, isTopRow, isLeftColumn, isRightColumn);
                cell.CellStyle = borderStyle;
            }
        }
    }

    /// <summary>
    /// Checks if row is a distribution header row
    /// </summary>
    private static bool IsDistributionHeaderRow(IRow row)
    {
        var firstCell = row.GetCell(0);
        if (firstCell != null)
        {
            var cellValue = firstCell.StringCellValue;
            return cellValue == MassBalanceExcelConstants.DistributionTitle || cellValue == MassBalanceExcelConstants.RecyclingAreaKeyword;
        }
        return false;
    }

    /// <summary>
    /// Creates border style for distribution table cells
    /// </summary>
    private static ICellStyle CreateDistributionBorderStyle(IWorkbook workbook, int col, bool isHeaderRow, bool isBottomRow, bool isTopRow, bool isLeftColumn, bool isRightColumn)
    {
        ICellStyle borderStyle;

        if (isHeaderRow)
        {
            borderStyle = workbook.CreateCellStyle();
            borderStyle.CloneStyleFrom(CreateHeaderStyle(workbook));
            borderStyle.BorderTop = BorderStyle.Thick;
            borderStyle.BorderBottom = BorderStyle.Thick;
            borderStyle.BorderLeft = BorderStyle.Thick;
            borderStyle.BorderRight = BorderStyle.Thick;
        }
        else if (isBottomRow)
        {
            if (MassBalanceExcelConstants.DistributionIntegerColumns.Contains(col))
            {
                borderStyle = CreateDistributionTableIntegerBorderStyle(workbook, false, false, true, false, false);
            }
            else if (MassBalanceExcelConstants.DistributionCompensationColumns.Contains(col))
            {
                borderStyle = CreateDistributionTableCompensationBorderStyle(workbook, false, false, true, false, false);
            }
            else
            {
                borderStyle = CreateDistributionTableBorderStyle(workbook, false, false, true, false, false);
            }

            // Override to thick borders on all sides for totals
            borderStyle.BorderTop = BorderStyle.Thick;
            borderStyle.BorderBottom = BorderStyle.Thick;
            borderStyle.BorderLeft = BorderStyle.Thick;
            borderStyle.BorderRight = BorderStyle.Thick;
        }
        else
        {
            if (MassBalanceExcelConstants.DistributionIntegerColumns.Contains(col))
            {
                borderStyle = CreateDistributionTableIntegerBorderStyle(workbook, true, isTopRow, isBottomRow, isLeftColumn, isRightColumn);
            }
            else if (MassBalanceExcelConstants.DistributionCompensationColumns.Contains(col))
            {
                borderStyle = CreateDistributionTableCompensationBorderStyle(workbook, true, isTopRow, isBottomRow, isLeftColumn, isRightColumn);
            }
            else
            {
                borderStyle = CreateDistributionTableBorderStyle(workbook, true, isTopRow, isBottomRow, isLeftColumn, isRightColumn);
            }
        }

        return borderStyle;
    }

    /// <summary>
    /// Creates distribution table border style
    /// </summary>
    private static ICellStyle CreateDistributionTableBorderStyle(IWorkbook workbook, bool isOuterBorder = false, bool isTopRow = false, bool isBottomRow = false, bool isLeftColumn = false, bool isRightColumn = false)
    {
        var style = workbook.CreateCellStyle();
        style.DataFormat = workbook.CreateDataFormat().GetFormat(MassBalanceExcelConstants.DecimalFormat);

        style.BorderTop = (isOuterBorder && isTopRow) || isBottomRow ? BorderStyle.Thick : BorderStyle.Thin;
        style.BorderBottom = (isOuterBorder && isBottomRow) || isBottomRow ? BorderStyle.Thick : BorderStyle.Thin;
        style.BorderLeft = (isOuterBorder && isLeftColumn) ? BorderStyle.Thick : BorderStyle.Thin;
        style.BorderRight = (isOuterBorder && isRightColumn) ? BorderStyle.Thick : BorderStyle.Thin;

        return style;
    }

    /// <summary>
    /// Creates distribution table integer border style
    /// </summary>
    private static ICellStyle CreateDistributionTableIntegerBorderStyle(IWorkbook workbook, bool isOuterBorder = false, bool isTopRow = false, bool isBottomRow = false, bool isLeftColumn = false, bool isRightColumn = false)
    {
        var style = workbook.CreateCellStyle();
        style.DataFormat = workbook.CreateDataFormat().GetFormat(MassBalanceExcelConstants.IntegerFormat);

        style.BorderTop = (isOuterBorder && isTopRow) || isBottomRow ? BorderStyle.Thick : BorderStyle.Thin;
        style.BorderBottom = (isOuterBorder && isBottomRow) || isBottomRow ? BorderStyle.Thick : BorderStyle.Thin;
        style.BorderLeft = (isOuterBorder && isLeftColumn) ? BorderStyle.Thick : BorderStyle.Thin;
        style.BorderRight = (isOuterBorder && isRightColumn) ? BorderStyle.Thick : BorderStyle.Thin;

        return style;
    }

    /// <summary>
    /// Creates distribution table compensation border style
    /// </summary>
    private static ICellStyle CreateDistributionTableCompensationBorderStyle(IWorkbook workbook, bool isOuterBorder = false, bool isTopRow = false, bool isBottomRow = false, bool isLeftColumn = false, bool isRightColumn = false)
    {
        var style = workbook.CreateCellStyle();
        var font = workbook.CreateFont();
        font.IsBold = true;
        style.SetFont(font);
        style.DataFormat = workbook.CreateDataFormat().GetFormat(MassBalanceExcelConstants.CompensationFormat);
        style.FillForegroundColor = IndexedColors.LightYellow.Index;
        style.FillPattern = FillPattern.SolidForeground;

        style.BorderTop = (isOuterBorder && isTopRow) || isBottomRow ? BorderStyle.Thick : BorderStyle.Thin;
        style.BorderBottom = (isOuterBorder && isBottomRow) || isBottomRow ? BorderStyle.Thick : BorderStyle.Thin;
        style.BorderLeft = (isOuterBorder && isLeftColumn) ? BorderStyle.Thick : BorderStyle.Thin;
        style.BorderRight = (isOuterBorder && isRightColumn) ? BorderStyle.Thick : BorderStyle.Thin;

        return style;
    }

    /// <summary>
    /// Applies borders to a merged region
    /// </summary>
    private static void ApplyBordersToMergedRegion(ISheet sheet, IWorkbook workbook, CellRangeAddress mergedRegion)
    {
        for (int row = mergedRegion.FirstRow; row <= mergedRegion.LastRow; row++)
        {
            var sheetRow = sheet.GetRow(row);
            if (sheetRow != null)
            {
                for (int col = mergedRegion.FirstColumn; col <= mergedRegion.LastColumn; col++)
                {
                    var cell = sheetRow.GetCell(col);
                    if (cell != null)
                    {
                        var currentStyle = cell.CellStyle;
                        var newStyle = workbook.CreateCellStyle();
                        newStyle.CloneStyleFrom(currentStyle);

                        bool isTopRow = row == mergedRegion.FirstRow;
                        bool isBottomRow = row == mergedRegion.LastRow;
                        bool isLeftColumn = col == mergedRegion.FirstColumn;
                        bool isRightColumn = col == mergedRegion.LastColumn;

                        if (isTopRow && currentStyle.BorderTop != BorderStyle.None)
                        {
                            newStyle.BorderTop = currentStyle.BorderTop;
                            newStyle.TopBorderColor = currentStyle.TopBorderColor;
                        }
                        if (isBottomRow && currentStyle.BorderBottom != BorderStyle.None)
                        {
                            newStyle.BorderBottom = currentStyle.BorderBottom;
                            newStyle.BottomBorderColor = currentStyle.BottomBorderColor;
                        }
                        if (isLeftColumn && currentStyle.BorderLeft != BorderStyle.None)
                        {
                            newStyle.BorderLeft = currentStyle.BorderLeft;
                            newStyle.LeftBorderColor = currentStyle.LeftBorderColor;
                        }
                        if (isRightColumn && currentStyle.BorderRight != BorderStyle.None)
                        {
                            newStyle.BorderRight = currentStyle.BorderRight;
                            newStyle.RightBorderColor = currentStyle.RightBorderColor;
                        }

                        cell.CellStyle = newStyle;
                    }
                }
            }
        }
    }

    #endregion

    #region Helper Classes

    /// <summary>
    /// Container for Excel cell styles
    /// </summary>
    private class ExcelStyles
    {
        public ICellStyle TitleStyle { get; set; } = null!;
        public ICellStyle HeaderStyle { get; set; } = null!;
        public ICellStyle DataStyle { get; set; } = null!;
        public ICellStyle IntegerStyle { get; set; } = null!;
        public ICellStyle CalculatedStyle { get; set; } = null!;
        public ICellStyle CompensationStyle { get; set; } = null!;
        public ICellStyle TotalStyle { get; set; } = null!;
    }

    #endregion
}
